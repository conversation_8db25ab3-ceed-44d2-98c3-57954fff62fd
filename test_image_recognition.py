#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图像识别功能
用于验证按钮图片是否能被正确识别
"""

import cv2
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_image_files():
    """测试图像文件是否存在且可读取"""
    button_images = [
        "states/image/button1.png",
        "states/image/button2.png"
    ]
    
    logger.info("开始测试图像文件...")
    
    for i, img_path in enumerate(button_images, 1):
        logger.info(f"测试第{i}个按钮图片: {img_path}")
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            logger.error(f"❌ 文件不存在: {img_path}")
            continue
        
        # 尝试读取图片
        try:
            image = cv2.imread(img_path, cv2.IMREAD_COLOR)
            if image is None:
                logger.error(f"❌ 无法读取图片: {img_path}")
                continue
            
            height, width, channels = image.shape
            logger.info(f"✅ 图片读取成功: {img_path}")
            logger.info(f"   尺寸: {width}x{height}, 通道数: {channels}")
            
            # 检查图片是否过小
            if width < 10 or height < 10:
                logger.warning(f"⚠️  图片尺寸过小，可能影响识别效果")
            
            # 检查图片是否过大
            if width > 500 or height > 500:
                logger.warning(f"⚠️  图片尺寸较大，建议裁剪到按钮区域")
                
        except Exception as e:
            logger.error(f"❌ 读取图片时发生错误: {e}")
    
    logger.info("图像文件测试完成")

def display_image_info():
    """显示图像信息"""
    button_images = [
        "states/image/button1.png",
        "states/image/button2.png"
    ]
    
    print("\n" + "="*50)
    print("图像文件信息")
    print("="*50)
    
    for i, img_path in enumerate(button_images, 1):
        if os.path.exists(img_path):
            try:
                image = cv2.imread(img_path, cv2.IMREAD_COLOR)
                if image is not None:
                    height, width, channels = image.shape
                    file_size = os.path.getsize(img_path)
                    
                    print(f"\n按钮{i} ({img_path}):")
                    print(f"  尺寸: {width} x {height} 像素")
                    print(f"  通道数: {channels}")
                    print(f"  文件大小: {file_size} 字节")
                    print(f"  状态: ✅ 正常")
                else:
                    print(f"\n按钮{i} ({img_path}):")
                    print(f"  状态: ❌ 无法读取")
            except Exception as e:
                print(f"\n按钮{i} ({img_path}):")
                print(f"  状态: ❌ 错误 - {e}")
        else:
            print(f"\n按钮{i} ({img_path}):")
            print(f"  状态: ❌ 文件不存在")
    
    print("\n" + "="*50)

def main():
    """主函数"""
    print("图像识别功能测试")
    print("="*30)
    
    # 测试图像文件
    test_image_files()
    
    # 显示详细信息
    display_image_info()
    
    print("\n建议:")
    print("1. 确保按钮图片清晰且包含完整的按钮区域")
    print("2. 图片尺寸不要过大，建议裁剪到按钮周围")
    print("3. 避免包含过多背景内容")
    print("4. 如果识别效果不好，可以尝试调整匹配阈值")

if __name__ == "__main__":
    main()
