#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动下载Edge驱动程序
当自动下载失败时使用此脚本
"""

import os
import sys
import requests
import zipfile
import json
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_edge_version():
    """获取系统中Edge浏览器版本"""
    try:
        import subprocess
        import re
        
        # Windows系统获取Edge版本
        if os.name == 'nt':
            try:
                # 方法1: 通过注册表
                result = subprocess.run([
                    'reg', 'query', 
                    'HKEY_CURRENT_USER\\Software\\Microsoft\\Edge\\BLBeacon', 
                    '/v', 'version'
                ], capture_output=True, text=True, shell=True)
                
                if result.returncode == 0:
                    version_line = result.stdout.strip().split('\n')[-1]
                    version = version_line.split()[-1]
                    logger.info(f"通过注册表获取Edge版本: {version}")
                    return version
            except Exception:
                pass
            
            # 方法2: 通过PowerShell
            try:
                result = subprocess.run([
                    'powershell', '-Command',
                    '(Get-Item "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe").VersionInfo.FileVersion'
                ], capture_output=True, text=True, shell=True)
                
                if result.returncode == 0:
                    version = result.stdout.strip()
                    logger.info(f"通过PowerShell获取Edge版本: {version}")
                    return version
            except Exception:
                pass
            
            # 方法3: 检查常见路径
            edge_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe"
            ]
            
            for path in edge_paths:
                if os.path.exists(path):
                    try:
                        result = subprocess.run([
                            'powershell', '-Command',
                            f'(Get-Item "{path}").VersionInfo.FileVersion'
                        ], capture_output=True, text=True, shell=True)
                        
                        if result.returncode == 0:
                            version = result.stdout.strip()
                            logger.info(f"从路径 {path} 获取Edge版本: {version}")
                            return version
                    except Exception:
                        continue
        
        logger.warning("无法自动获取Edge版本")
        return None
        
    except Exception as e:
        logger.error(f"获取Edge版本失败: {e}")
        return None

def download_edge_driver(version=None):
    """下载Edge驱动"""
    try:
        if not version:
            version = get_edge_version()
            if not version:
                logger.error("无法获取Edge版本，请手动指定版本")
                return False
        
        # 获取主版本号
        major_version = version.split('.')[0]
        logger.info(f"Edge主版本号: {major_version}")
        
        # 构建下载URL
        base_url = "https://msedgedriver.azureedge.net"
        
        # 尝试不同的版本格式
        possible_versions = [version, f"{major_version}.0.0.0"]
        
        for try_version in possible_versions:
            try:
                # 64位Windows
                download_url = f"{base_url}/{try_version}/edgedriver_win64.zip"
                logger.info(f"尝试下载: {download_url}")
                
                response = requests.get(download_url, timeout=30)
                if response.status_code == 200:
                    logger.info("✅ 下载成功")
                    
                    # 保存zip文件
                    zip_path = "edgedriver.zip"
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    # 解压文件
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(".")
                    
                    # 删除zip文件
                    os.remove(zip_path)
                    
                    # 检查是否解压成功
                    if os.path.exists("msedgedriver.exe"):
                        logger.info("✅ EdgeDriver下载并解压成功")
                        logger.info("文件位置: msedgedriver.exe")
                        return True
                    else:
                        logger.error("解压后未找到msedgedriver.exe")
                        return False
                
            except Exception as e:
                logger.warning(f"版本 {try_version} 下载失败: {e}")
                continue
        
        logger.error("所有版本都下载失败")
        return False
        
    except Exception as e:
        logger.error(f"下载Edge驱动失败: {e}")
        return False

def main():
    """主函数"""
    print("Edge驱动手动下载工具")
    print("="*40)
    
    # 检查是否已存在驱动
    if os.path.exists("msedgedriver.exe"):
        print("✅ 当前目录已存在 msedgedriver.exe")
        choice = input("是否重新下载? (y/N): ").lower()
        if choice != 'y':
            return
    
    # 获取Edge版本
    version = get_edge_version()
    if version:
        print(f"检测到Edge版本: {version}")
    else:
        print("无法自动检测Edge版本")
        version = input("请手动输入Edge版本号 (例如: 120.0.2210.144): ").strip()
        if not version:
            print("❌ 未提供版本号，退出")
            return
    
    # 下载驱动
    print("\n开始下载Edge驱动...")
    if download_edge_driver(version):
        print("\n✅ Edge驱动下载成功!")
        print("现在可以运行自动化脚本了")
    else:
        print("\n❌ Edge驱动下载失败")
        print("\n手动下载步骤:")
        print("1. 访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
        print("2. 下载对应版本的EdgeDriver")
        print("3. 解压后将 msedgedriver.exe 放在脚本目录")

if __name__ == "__main__":
    main()
