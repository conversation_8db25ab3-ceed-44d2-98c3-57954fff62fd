#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化脚本：使用Selenium和图像识别自动操作网页
功能：
1. 打开浏览器访问指定网页
2. 滚动到页面底部
3. 使用图像识别找到按钮并点击
"""

import time
import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import os
import logging
from PIL import Image
import io

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 可选：设置为无头模式（不显示浏览器窗口）
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 初始化WebDriver (自动下载和管理ChromeDriver)
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            logger.info("浏览器驱动初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"浏览器驱动初始化失败: {e}")
            return False
    
    def open_website(self, url):
        """打开指定网站"""
        try:
            logger.info(f"正在访问网站: {url}")
            self.driver.get(url)
            time.sleep(3)  # 等待页面加载
            logger.info("网站访问成功")
            return True
        except Exception as e:
            logger.error(f"访问网站失败: {e}")
            return False
    
    def scroll_to_bottom(self):
        """滚动到页面底部"""
        try:
            logger.info("开始滚动到页面底部")
            
            # 获取页面高度
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            
            while True:
                # 滚动到底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                
                # 等待页面加载
                time.sleep(2)
                
                # 计算新的滚动高度并与上次滚动高度进行比较
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
            
            logger.info("已滚动到页面底部")
            return True
            
        except Exception as e:
            logger.error(f"滚动页面失败: {e}")
            return False
    
    def take_screenshot(self):
        """截取当前页面截图"""
        try:
            screenshot = self.driver.get_screenshot_as_png()
            screenshot_image = Image.open(io.BytesIO(screenshot))
            screenshot_cv = cv2.cvtColor(np.array(screenshot_image), cv2.COLOR_RGB2BGR)
            return screenshot_cv
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None
    
    def find_button_by_image(self, template_path, screenshot=None, threshold=0.8):
        """使用模板匹配找到按钮位置"""
        try:
            if not os.path.exists(template_path):
                logger.error(f"模板图片不存在: {template_path}")
                return None
            
            # 读取模板图片
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                logger.error(f"无法读取模板图片: {template_path}")
                return None
            
            # 如果没有提供截图，则截取当前页面
            if screenshot is None:
                screenshot = self.take_screenshot()
                if screenshot is None:
                    return None
            
            # 执行模板匹配
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)
            
            logger.info(f"模板匹配结果 - 最大相似度: {max_val:.3f}, 阈值: {threshold}")
            
            if max_val >= threshold:
                # 计算按钮中心位置
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                logger.info(f"找到按钮位置: ({center_x}, {center_y})")
                return (center_x, center_y)
            else:
                logger.warning(f"未找到匹配的按钮，相似度 {max_val:.3f} 低于阈值 {threshold}")
                return None
                
        except Exception as e:
            logger.error(f"图像识别失败: {e}")
            return None
    
    def click_at_position(self, x, y):
        """在指定位置点击"""
        try:
            # 使用JavaScript点击指定位置
            self.driver.execute_script(f"document.elementFromPoint({x}, {y}).click();")
            logger.info(f"已点击位置: ({x}, {y})")
            time.sleep(1)  # 等待点击效果
            return True
        except Exception as e:
            logger.error(f"点击失败: {e}")
            return False
    
    def find_and_click_buttons(self, button_images):
        """查找并点击所有按钮"""
        try:
            # 截取当前页面截图
            screenshot = self.take_screenshot()
            if screenshot is None:
                return False
            
            success_count = 0
            for i, button_path in enumerate(button_images, 1):
                logger.info(f"正在查找第{i}个按钮: {button_path}")
                
                # 查找按钮位置
                position = self.find_button_by_image(button_path, screenshot)
                
                if position:
                    # 点击按钮
                    if self.click_at_position(position[0], position[1]):
                        success_count += 1
                        logger.info(f"第{i}个按钮点击成功")
                        time.sleep(2)  # 等待页面响应
                        
                        # 重新截图，因为页面可能已经改变
                        screenshot = self.take_screenshot()
                    else:
                        logger.error(f"第{i}个按钮点击失败")
                else:
                    logger.error(f"未找到第{i}个按钮")
            
            logger.info(f"总共成功点击 {success_count}/{len(button_images)} 个按钮")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"查找和点击按钮过程失败: {e}")
            return False
    
    def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    # 目标网站
    target_url = "https://app.tangoapp.dev/guide"
    
    # 按钮图片路径
    button_images = [
        "states/image/button1.png",
        "states/image/button2.png"
    ]
    
    # 检查图片文件是否存在
    for img_path in button_images:
        if not os.path.exists(img_path):
            logger.error(f"按钮图片不存在: {img_path}")
            return False
    
    # 创建自动化实例
    automation = WebAutomation()
    
    try:
        # 1. 初始化浏览器
        if not automation.setup_driver():
            return False
        
        # 2. 打开网站
        if not automation.open_website(target_url):
            return False
        
        # 3. 滚动到页面底部
        if not automation.scroll_to_bottom():
            return False
        
        # 4. 查找并点击按钮
        if automation.find_and_click_buttons(button_images):
            logger.info("自动化脚本执行成功！")
            return True
        else:
            logger.error("未能成功点击任何按钮")
            return False
            
    except Exception as e:
        logger.error(f"脚本执行过程中发生错误: {e}")
        return False
        
    finally:
        # 5. 关闭浏览器
        automation.close_driver()

if __name__ == "__main__":
    main()
