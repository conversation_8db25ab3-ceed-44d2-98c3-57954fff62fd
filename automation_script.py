#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化脚本：使用Selenium和图像识别自动操作网页
功能：
1. 打开浏览器访问指定网页
2. 滚动到页面底部
3. 使用图像识别找到按钮并点击
"""

import time
import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
import os
import logging
from PIL import Image
import io

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def setup_driver(self):
        """设置Edge浏览器驱动"""
        try:
            edge_options = Options()
            # 可选：设置为无头模式（不显示浏览器窗口）
            # edge_options.add_argument('--headless')
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')
            edge_options.add_argument('--disable-gpu')
            edge_options.add_argument('--window-size=1920,1080')
            edge_options.add_argument('--disable-web-security')
            edge_options.add_argument('--allow-running-insecure-content')

            # 尝试多种方式初始化Edge驱动
            logger.info("正在初始化Edge浏览器驱动...")

            # 方法1: 尝试自动下载驱动
            try:
                logger.info("尝试自动下载Edge驱动...")
                service = Service(EdgeChromiumDriverManager().install())
                self.driver = webdriver.Edge(service=service, options=edge_options)
                logger.info("✅ 自动下载驱动成功")
            except Exception as e1:
                logger.warning(f"自动下载驱动失败: {e1}")

                # 方法2: 尝试使用系统已安装的驱动
                try:
                    logger.info("尝试使用系统Edge驱动...")
                    self.driver = webdriver.Edge(options=edge_options)
                    logger.info("✅ 使用系统驱动成功")
                except Exception as e2:
                    logger.warning(f"使用系统驱动失败: {e2}")

                    # 方法3: 尝试指定常见的驱动路径
                    common_paths = [
                        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedgedriver.exe",
                        r"C:\Program Files\Microsoft\Edge\Application\msedgedriver.exe",
                        r"msedgedriver.exe"  # 如果在PATH中
                    ]

                    driver_found = False
                    for path in common_paths:
                        try:
                            logger.info(f"尝试使用驱动路径: {path}")
                            if os.path.exists(path) or path == "msedgedriver.exe":
                                service = Service(path)
                                self.driver = webdriver.Edge(service=service, options=edge_options)
                                logger.info(f"✅ 使用驱动路径成功: {path}")
                                driver_found = True
                                break
                        except Exception as e3:
                            logger.warning(f"路径 {path} 失败: {e3}")
                            continue

                    if not driver_found:
                        raise Exception("所有驱动初始化方法都失败了")

            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Edge浏览器驱动初始化成功")
            return True

        except Exception as e:
            logger.error(f"Edge浏览器驱动初始化失败: {e}")
            logger.error("请尝试以下解决方案:")
            logger.error("1. 检查网络连接")
            logger.error("2. 确保Microsoft Edge浏览器已安装")
            logger.error("3. 手动下载EdgeDriver并放在脚本目录")
            logger.error("4. 或者运行: pip install --upgrade webdriver-manager")
            return False
    
    def open_website(self, url):
        """打开指定网站"""
        try:
            logger.info(f"正在访问网站: {url}")
            self.driver.get(url)
            time.sleep(3)  # 等待页面加载
            logger.info("网站访问成功")
            return True
        except Exception as e:
            logger.error(f"访问网站失败: {e}")
            return False
    
    def handle_cookie_popup(self):
        """处理Cookie弹窗"""
        try:
            logger.info("检查并处理Cookie弹窗...")

            # 常见的Cookie弹窗选择器
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                "button[id*='cookie']",
                "button[class*='cookie']",
                "button[id*='consent']",
                "button[class*='consent']",
                "[data-testid*='accept']",
                "[data-testid*='cookie']",
                "button:contains('Accept')",
                "button:contains('同意')",
                "button:contains('接受')",
                "button:contains('确定')",
                ".cookie-accept",
                ".accept-cookies",
                "#cookie-accept",
                "#accept-cookies"
            ]

            # 尝试找到并点击Cookie接受按钮
            for selector in cookie_selectors:
                try:
                    # 使用JavaScript查找元素
                    script = f"""
                    var elements = document.querySelectorAll('{selector}');
                    if (elements.length > 0) {{
                        for (var i = 0; i < elements.length; i++) {{
                            var elem = elements[i];
                            if (elem.offsetParent !== null) {{ // 检查元素是否可见
                                elem.click();
                                return true;
                            }}
                        }}
                    }}
                    return false;
                    """

                    result = self.driver.execute_script(script)
                    if result:
                        logger.info(f"成功点击Cookie弹窗按钮: {selector}")
                        time.sleep(1)
                        return True

                except Exception:
                    continue

            # 尝试按ESC键关闭弹窗
            try:
                actions = ActionChains(self.driver)
                actions.send_keys(Keys.ESCAPE).perform()
                time.sleep(1)
                logger.info("尝试使用ESC键关闭弹窗")
            except Exception:
                pass

            logger.info("Cookie弹窗处理完成")
            return True

        except Exception as e:
            logger.error(f"处理Cookie弹窗失败: {e}")
            return False

    def scroll_to_bottom(self):
        """滚动到页面底部"""
        try:
            logger.info("开始滚动到页面底部")

            # 先处理Cookie弹窗
            self.handle_cookie_popup()

            # 获取页面高度
            last_height = self.driver.execute_script("return document.body.scrollHeight")

            while True:
                # 滚动到底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

                # 等待页面加载
                time.sleep(2)

                # 再次检查Cookie弹窗（有些弹窗在滚动后才出现）
                self.handle_cookie_popup()

                # 计算新的滚动高度并与上次滚动高度进行比较
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height

            logger.info("已滚动到页面底部")
            return True

        except Exception as e:
            logger.error(f"滚动页面失败: {e}")
            return False
    
    def take_screenshot(self):
        """截取当前页面截图"""
        try:
            screenshot = self.driver.get_screenshot_as_png()
            screenshot_image = Image.open(io.BytesIO(screenshot))
            screenshot_cv = cv2.cvtColor(np.array(screenshot_image), cv2.COLOR_RGB2BGR)
            return screenshot_cv
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None
    
    def find_button_by_image(self, template_path, screenshot=None, threshold=0.4):
        """使用模板匹配找到按钮位置，返回最佳可点击位置"""
        try:
            if not os.path.exists(template_path):
                logger.error(f"模板图片不存在: {template_path}")
                return None

            # 读取模板图片
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                logger.error(f"无法读取模板图片: {template_path}")
                return None

            # 如果没有提供截图，则截取当前页面
            if screenshot is None:
                screenshot = self.take_screenshot()
                if screenshot is None:
                    return None

            # 执行模板匹配
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)

            # 找到所有匹配位置
            locations = np.where(result >= threshold)
            matches = []

            h, w = template.shape[:2]

            for pt in zip(*locations[::-1]):  # 切换x和y坐标
                confidence = result[pt[1], pt[0]]
                center_x = pt[0] + w // 2
                center_y = pt[1] + h // 2
                matches.append({
                    'position': (center_x, center_y),
                    'confidence': confidence,
                    'top_left': pt,
                    'bottom_right': (pt[0] + w, pt[1] + h)
                })

            if not matches:
                # 如果没有找到匹配，尝试降低阈值
                _, max_val, _, max_loc = cv2.minMaxLoc(result)
                logger.warning(f"未找到匹配的按钮，最高相似度: {max_val:.3f}, 阈值: {threshold}")

                if max_val >= 0.2:  # 如果相似度还算可以，尝试使用
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2
                    logger.info(f"尝试使用低相似度匹配: ({center_x}, {center_y}), 相似度: {max_val:.3f}")
                    return (center_x, center_y)

                return None

            # 按置信度排序
            matches.sort(key=lambda x: x['confidence'], reverse=True)

            logger.info(f"找到 {len(matches)} 个匹配位置")

            # 尝试每个匹配位置，找到第一个可点击的
            for i, match in enumerate(matches):
                position = match['position']
                confidence = match['confidence']

                logger.info(f"检查匹配 {i+1}: 位置 {position}, 相似度: {confidence:.3f}")

                # 检查这个位置是否可点击
                clickable_info = self.check_element_clickable(position[0], position[1])

                if clickable_info.get('clickable', False):
                    logger.info(f"✅ 找到可点击的按钮位置: {position}")
                    return position
                else:
                    logger.warning(f"位置 {position} 不可点击: {clickable_info.get('reasons', [])}")

                    # 尝试周围的位置
                    for offset_x in [-10, 0, 10]:
                        for offset_y in [-10, 0, 10]:
                            if offset_x == 0 and offset_y == 0:
                                continue

                            test_x = position[0] + offset_x
                            test_y = position[1] + offset_y

                            test_clickable = self.check_element_clickable(test_x, test_y)
                            if test_clickable.get('clickable', False):
                                logger.info(f"✅ 在偏移位置找到可点击元素: ({test_x}, {test_y})")
                                return (test_x, test_y)

            # 如果所有位置都不可点击，返回置信度最高的位置
            best_match = matches[0]
            logger.warning(f"所有匹配位置都不可点击，返回最佳匹配: {best_match['position']}")
            return best_match['position']

        except Exception as e:
            logger.error(f"图像识别失败: {e}")
            return None
    
    def check_element_clickable(self, x, y):
        """检查指定位置的元素是否可点击"""
        try:
            script = f"""
            var element = document.elementFromPoint({x}, {y});
            if (!element) return {{clickable: false, reason: '未找到元素'}};

            var computedStyle = window.getComputedStyle(element);
            var clickable = false;
            var reasons = [];

            // 检查元素是否可见
            if (computedStyle.display === 'none') {{
                reasons.push('元素display为none');
            }}
            if (computedStyle.visibility === 'hidden') {{
                reasons.push('元素visibility为hidden');
            }}
            if (computedStyle.opacity === '0') {{
                reasons.push('元素opacity为0');
            }}

            // 检查元素是否可点击
            if (element.tagName === 'BUTTON' ||
                element.tagName === 'A' ||
                element.onclick ||
                element.getAttribute('onclick') ||
                computedStyle.cursor === 'pointer' ||
                element.getAttribute('role') === 'button') {{
                clickable = true;
            }}

            // 检查父元素是否可点击
            var parent = element.parentElement;
            while (parent && parent !== document.body && !clickable) {{
                var parentStyle = window.getComputedStyle(parent);
                if (parent.tagName === 'BUTTON' ||
                    parent.tagName === 'A' ||
                    parent.onclick ||
                    parent.getAttribute('onclick') ||
                    parentStyle.cursor === 'pointer' ||
                    parent.getAttribute('role') === 'button') {{
                    clickable = true;
                    reasons.push('父元素可点击');
                    break;
                }}
                parent = parent.parentElement;
            }}

            return {{
                clickable: clickable,
                reasons: reasons,
                tagName: element.tagName,
                hasClick: !!element.click,
                cursor: computedStyle.cursor,
                display: computedStyle.display,
                visibility: computedStyle.visibility
            }};
            """

            result = self.driver.execute_script(script)
            return result

        except Exception as e:
            logger.error(f"检查元素可点击性失败: {e}")
            return {"clickable": False, "reason": f"检查失败: {e}"}

    def get_element_dom_info(self, x, y):
        """获取指定位置元素的DOM信息"""
        try:
            script = f"""
            var element = document.elementFromPoint({x}, {y});
            if (element) {{
                var info = {{
                    tagName: element.tagName,
                    id: element.id || '',
                    className: element.className || '',
                    textContent: element.textContent ? element.textContent.trim().substring(0, 100) : '',
                    innerHTML: element.innerHTML ? element.innerHTML.substring(0, 200) : '',
                    attributes: {{}},
                    style: {{
                        display: window.getComputedStyle(element).display,
                        visibility: window.getComputedStyle(element).visibility,
                        position: window.getComputedStyle(element).position,
                        cursor: window.getComputedStyle(element).cursor
                    }},
                    boundingRect: element.getBoundingClientRect(),
                    clickable: !!(element.click || element.onclick || element.getAttribute('onclick'))
                }};

                // 获取所有属性
                for (var i = 0; i < element.attributes.length; i++) {{
                    var attr = element.attributes[i];
                    info.attributes[attr.name] = attr.value;
                }}

                return info;
            }}
            return null;
            """

            dom_info = self.driver.execute_script(script)
            return dom_info

        except Exception as e:
            logger.error(f"获取DOM信息失败: {e}")
            return None

    def click_at_position(self, x, y):
        """在指定位置点击并返回DOM信息"""
        try:
            # 先检查元素是否可点击
            clickable_info = self.check_element_clickable(x, y)
            logger.info(f"元素可点击性检查: {clickable_info}")

            # 先获取DOM信息
            dom_info = self.get_element_dom_info(x, y)

            if not dom_info:
                logger.error(f"位置 ({x}, {y}) 没有找到任何元素")
                return False, None

            logger.info(f"找到元素: {dom_info.get('tagName', 'N/A')} - {dom_info.get('textContent', 'N/A')[:30]}")

            # 多种点击方式尝试
            click_success = False

            # 方法1: 使用JavaScript点击（带空值检查）
            try:
                script = f"""
                var element = document.elementFromPoint({x}, {y});
                if (element && element.click) {{
                    element.click();
                    return true;
                }} else {{
                    return false;
                }}
                """
                result = self.driver.execute_script(script)
                if result:
                    logger.info(f"✅ JavaScript点击成功: ({x}, {y})")
                    click_success = True
                else:
                    logger.warning(f"JavaScript点击失败: 元素为null或无click方法")
            except Exception as e1:
                logger.warning(f"JavaScript点击失败: {e1}")

            # 方法2: 使用Selenium ActionChains点击
            if not click_success:
                try:
                    actions = ActionChains(self.driver)
                    actions.move_by_offset(x, y).click().perform()
                    # 重置鼠标位置
                    actions.move_by_offset(-x, -y).perform()
                    logger.info(f"✅ ActionChains点击成功: ({x}, {y})")
                    click_success = True
                except Exception as e2:
                    logger.warning(f"ActionChains点击失败: {e2}")

            # 方法3: 尝试找到可点击的父元素
            if not click_success:
                try:
                    script = f"""
                    var element = document.elementFromPoint({x}, {y});
                    var clickableElement = element;

                    // 向上查找可点击的元素
                    while (clickableElement && clickableElement !== document.body) {{
                        if (clickableElement.click ||
                            clickableElement.tagName === 'BUTTON' ||
                            clickableElement.tagName === 'A' ||
                            clickableElement.onclick ||
                            clickableElement.getAttribute('onclick') ||
                            clickableElement.style.cursor === 'pointer') {{
                            clickableElement.click();
                            return true;
                        }}
                        clickableElement = clickableElement.parentElement;
                    }}

                    // 如果找不到可点击元素，尝试触发点击事件
                    if (element) {{
                        var event = new MouseEvent('click', {{
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: {x},
                            clientY: {y}
                        }});
                        element.dispatchEvent(event);
                        return true;
                    }}

                    return false;
                    """

                    result = self.driver.execute_script(script)
                    if result:
                        logger.info(f"✅ 父元素或事件点击成功: ({x}, {y})")
                        click_success = True
                    else:
                        logger.warning(f"父元素点击失败: 未找到可点击元素")
                except Exception as e3:
                    logger.warning(f"父元素点击失败: {e3}")

            # 方法4: 使用坐标直接点击（最后尝试）
            if not click_success:
                try:
                    script = f"""
                    var event = new MouseEvent('click', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: {x},
                        clientY: {y}
                    }});
                    document.elementFromPoint({x}, {y}).dispatchEvent(event);
                    return true;
                    """

                    self.driver.execute_script(script)
                    logger.info(f"✅ 坐标事件点击: ({x}, {y})")
                    click_success = True
                except Exception as e4:
                    logger.error(f"坐标事件点击失败: {e4}")

            if not click_success:
                logger.error(f"❌ 所有点击方法都失败了: ({x}, {y})")
                return False, dom_info

            # 记录DOM信息
            if dom_info:
                logger.info(f"点击元素的DOM信息:")
                logger.info(f"  标签: {dom_info.get('tagName', 'N/A')}")
                logger.info(f"  ID: {dom_info.get('id', 'N/A')}")
                logger.info(f"  类名: {dom_info.get('className', 'N/A')}")
                logger.info(f"  文本内容: {dom_info.get('textContent', 'N/A')[:50]}...")

                # 打印详细的DOM信息
                print("\n" + "="*60)
                print(f"点击位置 ({x}, {y}) 的DOM信息:")
                print("="*60)
                print(f"标签名: {dom_info.get('tagName', 'N/A')}")
                print(f"ID: {dom_info.get('id', 'N/A')}")
                print(f"类名: {dom_info.get('className', 'N/A')}")
                print(f"文本内容: {dom_info.get('textContent', 'N/A')}")
                print(f"HTML内容: {dom_info.get('innerHTML', 'N/A')}")

                attributes = dom_info.get('attributes', {})
                if attributes:
                    print("属性:")
                    for attr_name, attr_value in attributes.items():
                        print(f"  {attr_name}: {attr_value}")

                style = dom_info.get('style', {})
                if style:
                    print("样式:")
                    for style_name, style_value in style.items():
                        print(f"  {style_name}: {style_value}")

                rect = dom_info.get('boundingRect', {})
                if rect:
                    print(f"位置和尺寸:")
                    print(f"  x: {rect.get('x', 'N/A')}, y: {rect.get('y', 'N/A')}")
                    print(f"  width: {rect.get('width', 'N/A')}, height: {rect.get('height', 'N/A')}")

                print("="*60)

            time.sleep(1)  # 等待点击效果
            return True, dom_info

        except Exception as e:
            logger.error(f"点击过程发生异常: {e}")
            return False, None
    
    def find_and_click_buttons(self, button_images):
        """查找并点击所有按钮，返回DOM信息"""
        try:
            # 先处理可能的弹窗
            self.handle_cookie_popup()

            # 截取当前页面截图
            screenshot = self.take_screenshot()
            if screenshot is None:
                return False, []

            success_count = 0
            dom_info_list = []

            for i, button_path in enumerate(button_images, 1):
                logger.info(f"正在查找第{i}个按钮: {button_path}")

                # 查找按钮位置
                position = self.find_button_by_image(button_path, screenshot)

                if position:
                    # 点击按钮并获取DOM信息
                    click_success, dom_info = self.click_at_position(position[0], position[1])

                    if click_success:
                        success_count += 1
                        logger.info(f"第{i}个按钮点击成功")

                        # 保存DOM信息
                        button_info = {
                            'button_index': i,
                            'button_path': button_path,
                            'position': position,
                            'dom_info': dom_info
                        }
                        dom_info_list.append(button_info)

                        time.sleep(2)  # 等待页面响应

                        # 再次处理可能出现的弹窗
                        self.handle_cookie_popup()

                        # 重新截图，因为页面可能已经改变
                        screenshot = self.take_screenshot()
                    else:
                        logger.error(f"第{i}个按钮点击失败")
                        dom_info_list.append({
                            'button_index': i,
                            'button_path': button_path,
                            'position': position,
                            'dom_info': None,
                            'error': '点击失败'
                        })
                else:
                    logger.error(f"未找到第{i}个按钮")
                    dom_info_list.append({
                        'button_index': i,
                        'button_path': button_path,
                        'position': None,
                        'dom_info': None,
                        'error': '未找到按钮'
                    })

            logger.info(f"总共成功点击 {success_count}/{len(button_images)} 个按钮")

            # 打印汇总信息
            print("\n" + "="*80)
            print("按钮点击汇总报告")
            print("="*80)
            for info in dom_info_list:
                print(f"\n按钮 {info['button_index']} ({info['button_path']}):")
                if info.get('error'):
                    print(f"  状态: ❌ {info['error']}")
                else:
                    print(f"  状态: ✅ 点击成功")
                    print(f"  位置: {info['position']}")
                    if info['dom_info']:
                        dom = info['dom_info']
                        print(f"  标签: {dom.get('tagName', 'N/A')}")
                        print(f"  ID: {dom.get('id', 'N/A')}")
                        print(f"  类名: {dom.get('className', 'N/A')}")
            print("="*80)

            return success_count > 0, dom_info_list

        except Exception as e:
            logger.error(f"查找和点击按钮过程失败: {e}")
            return False, []
    
    def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    # 目标网站
    target_url = "https://app.tangoapp.dev/guide"
    
    # 按钮图片路径
    button_images = [
        "states/image/button1.png",
        "states/image/button2.png"
    ]
    
    # 检查图片文件是否存在
    for img_path in button_images:
        if not os.path.exists(img_path):
            logger.error(f"按钮图片不存在: {img_path}")
            return False
    
    # 创建自动化实例
    automation = WebAutomation()
    
    try:
        # 1. 初始化浏览器
        if not automation.setup_driver():
            return False
        
        # 2. 打开网站
        if not automation.open_website(target_url):
            return False
        
        # 3. 滚动到页面底部
        if not automation.scroll_to_bottom():
            return False
        
        # 4. 查找并点击按钮
        success, dom_info_list = automation.find_and_click_buttons(button_images)
        if success:
            logger.info("自动化脚本执行成功！")

            # 保存DOM信息到文件
            try:
                import json
                with open('button_dom_info.json', 'w', encoding='utf-8') as f:
                    json.dump(dom_info_list, f, ensure_ascii=False, indent=2)
                logger.info("DOM信息已保存到 button_dom_info.json")
            except Exception as e:
                logger.warning(f"保存DOM信息失败: {e}")

            return True
        else:
            logger.error("未能成功点击任何按钮")
            return False
            
    except Exception as e:
        logger.error(f"脚本执行过程中发生错误: {e}")
        return False
        
    finally:
        # 5. 关闭浏览器
        automation.close_driver()

if __name__ == "__main__":
    main()
