@echo off
chcp 65001 >nul
echo ========================================
echo 网页自动化脚本运行器 (Edge浏览器)
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH环境变量
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在检查依赖包...
pip show selenium >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已安装
)
echo.

echo 正在检查图像文件...
if not exist "states\image\button1.png" (
    echo ❌ 缺少按钮图片: states\image\button1.png
    pause
    exit /b 1
)
if not exist "states\image\button2.png" (
    echo ❌ 缺少按钮图片: states\image\button2.png
    pause
    exit /b 1
)
echo ✅ 图像文件检查通过
echo.

echo 🚀 启动自动化脚本 (使用Edge浏览器)...
echo ========================================
python automation_script.py

if errorlevel 1 (
    echo.
    echo ❌ 脚本执行失败
    echo.
    echo 如果遇到Edge驱动问题，请尝试以下解决方案:
    echo 1. 双击 download_driver.bat 手动下载驱动
    echo 2. 检查网络连接
    echo 3. 确保Microsoft Edge浏览器已安装
    echo 4. 运行: pip install --upgrade webdriver-manager
    echo.
) else (
    echo.
    echo ✅ 脚本执行成功
    echo.
)

echo ========================================
echo 脚本执行完成
echo ========================================
pause
