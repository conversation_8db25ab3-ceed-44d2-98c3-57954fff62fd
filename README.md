# 网页自动化脚本

这个脚本使用Selenium和OpenCV实现网页自动化操作，包括图像识别和按钮点击功能。

## 功能特性

- 自动打开Chrome浏览器
- 访问指定网页 (https://app.tangoapp.dev/guide)
- 自动滚动到页面底部
- 使用图像识别技术找到页面中的按钮
- 依次点击识别到的按钮

## 环境要求

- Python 3.7+
- Chrome浏览器
- ChromeDriver (会自动下载)

## 安装步骤

1. **安装Python依赖包**
   ```bash
   pip install -r requirements.txt
   ```

2. **确保Chrome浏览器已安装**
   - 脚本会自动管理ChromeDriver，无需手动下载

3. **准备按钮图片**
   - 确保 `states/image/button1.png` 和 `states/image/button2.png` 文件存在
   - 这些图片应该是您想要点击的按钮的截图

## 使用方法

直接运行脚本：
```bash
python automation_script.py
```

## 脚本工作流程

1. **初始化浏览器**: 启动Chrome浏览器
2. **访问网页**: 打开目标网站
3. **滚动页面**: 自动滚动到页面最底部
4. **截取屏幕**: 获取当前页面截图
5. **图像识别**: 使用模板匹配找到按钮位置
6. **点击按钮**: 依次点击找到的按钮
7. **关闭浏览器**: 完成操作后关闭浏览器

## 配置选项

在 `automation_script.py` 中可以调整以下参数：

- `threshold`: 图像匹配阈值 (默认0.8)
- `target_url`: 目标网站URL
- Chrome浏览器选项 (无头模式、窗口大小等)

## 故障排除

1. **找不到按钮**: 
   - 检查按钮图片是否清晰
   - 调整匹配阈值
   - 确保页面完全加载

2. **浏览器启动失败**:
   - 确保Chrome浏览器已安装
   - 检查网络连接

3. **点击无效**:
   - 检查按钮是否可点击
   - 确认页面元素没有被遮挡

## 注意事项

- 脚本运行时请不要操作鼠标和键盘
- 确保网络连接稳定
- 按钮图片应该尽可能准确和清晰
- 建议在运行前先测试图像识别效果
