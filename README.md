# 网页自动化脚本

这个脚本使用Selenium和OpenCV实现网页自动化操作，包括图像识别和按钮点击功能。

## 功能特性

- 自动打开Microsoft Edge浏览器
- 访问指定网页 (https://app.tangoapp.dev/guide)
- **智能处理Cookie弹窗** - 自动识别并关闭各种Cookie同意弹窗
- 自动滚动到页面底部
- 使用图像识别技术找到页面中的按钮
- 依次点击识别到的按钮
- **获取DOM信息** - 点击按钮时自动获取并显示详细的DOM元素信息
- **保存结果** - 将DOM信息保存到JSON文件中

## 环境要求

- Python 3.7+
- Microsoft Edge浏览器
- EdgeDriver (会自动下载)

## 安装步骤

1. **安装Python依赖包**
   ```bash
   pip install -r requirements.txt
   ```

2. **确保Microsoft Edge浏览器已安装**
   - 脚本会自动管理EdgeDriver，无需手动下载

3. **准备按钮图片**
   - 确保 `states/image/button1.png` 和 `states/image/button2.png` 文件存在
   - 这些图片应该是您想要点击的按钮的截图

## 使用方法

直接运行脚本：
```bash
python automation_script.py
```

## 脚本工作流程

1. **初始化浏览器**: 启动Microsoft Edge浏览器
2. **访问网页**: 打开目标网站
3. **处理弹窗**: 自动识别并关闭Cookie同意弹窗
4. **滚动页面**: 自动滚动到页面最底部（滚动过程中持续处理弹窗）
5. **截取屏幕**: 获取当前页面截图
6. **图像识别**: 使用模板匹配找到按钮位置
7. **点击按钮**: 依次点击找到的按钮
8. **获取DOM信息**: 每次点击时获取元素的详细DOM信息
9. **保存结果**: 将DOM信息保存到 `button_dom_info.json` 文件
10. **关闭浏览器**: 完成操作后关闭浏览器

## 配置选项

在 `automation_script.py` 中可以调整以下参数：

- `threshold`: 图像匹配阈值 (默认0.4，范围0-1，越低越宽松)
- `target_url`: 目标网站URL
- Edge浏览器选项 (无头模式、窗口大小等)
- Cookie弹窗选择器列表 (可添加更多弹窗识别规则)

## 输出文件

脚本运行后会生成以下文件：

- `button_dom_info.json`: 包含所有点击按钮的详细DOM信息

## 故障排除

1. **找不到按钮**:
   - 检查按钮图片是否清晰
   - 调整匹配阈值 (在脚本中修改threshold值)
   - 确保页面完全加载
   - 运行 `test_images.bat` 验证图片文件

2. **浏览器启动失败**:
   - 确保Microsoft Edge浏览器已安装
   - 运行 `download_driver.bat` 手动下载驱动
   - 检查网络连接

3. **点击失败 (Cannot read properties of null)**:
   - 脚本现在使用多种点击方法自动重试
   - 运行 `test_click.bat` 测试点击功能
   - 检查元素是否被其他元素遮挡
   - 确认页面元素已完全加载

4. **图像识别问题**:
   - 降低匹配阈值 (当前默认0.4)
   - 确保按钮图片包含完整按钮区域
   - 避免图片包含过多背景内容

## 注意事项

- 脚本运行时请不要操作鼠标和键盘
- 确保网络连接稳定
- 按钮图片应该尽可能准确和清晰
- 建议在运行前先测试图像识别效果
