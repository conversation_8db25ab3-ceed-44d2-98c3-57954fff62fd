#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试点击功能
用于验证不同的点击方法是否正常工作
"""

import time
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.microsoft import EdgeChromiumDriverManager
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ClickTester:
    def __init__(self):
        self.driver = None
    
    def setup_driver(self):
        """设置Edge浏览器驱动"""
        try:
            edge_options = Options()
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')
            edge_options.add_argument('--disable-gpu')
            edge_options.add_argument('--window-size=1920,1080')
            
            # 尝试初始化驱动
            try:
                service = Service(EdgeChromiumDriverManager().install())
                self.driver = webdriver.Edge(service=service, options=edge_options)
            except:
                self.driver = webdriver.Edge(options=edge_options)
            
            logger.info("Edge浏览器驱动初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Edge浏览器驱动初始化失败: {e}")
            return False
    
    def test_click_methods(self, x, y):
        """测试不同的点击方法"""
        results = {}
        
        # 方法1: JavaScript点击（带空值检查）
        try:
            script = f"""
            var element = document.elementFromPoint({x}, {y});
            if (element && element.click) {{
                element.click();
                return {{success: true, element: element.tagName, text: element.textContent ? element.textContent.trim().substring(0, 50) : ''}};
            }} else {{
                return {{success: false, reason: 'Element is null or has no click method', element: element ? element.tagName : 'null'}};
            }}
            """
            result = self.driver.execute_script(script)
            results['javascript'] = result
            logger.info(f"JavaScript点击结果: {result}")
        except Exception as e:
            results['javascript'] = {'success': False, 'error': str(e)}
            logger.error(f"JavaScript点击失败: {e}")
        
        time.sleep(1)
        
        # 方法2: ActionChains点击
        try:
            actions = ActionChains(self.driver)
            actions.move_by_offset(x, y).click().perform()
            actions.move_by_offset(-x, -y).perform()
            results['actionchains'] = {'success': True}
            logger.info("ActionChains点击成功")
        except Exception as e:
            results['actionchains'] = {'success': False, 'error': str(e)}
            logger.error(f"ActionChains点击失败: {e}")
        
        time.sleep(1)
        
        # 方法3: 事件分发
        try:
            script = f"""
            var element = document.elementFromPoint({x}, {y});
            if (element) {{
                var event = new MouseEvent('click', {{
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: {x},
                    clientY: {y}
                }});
                element.dispatchEvent(event);
                return {{success: true, element: element.tagName}};
            }} else {{
                return {{success: false, reason: 'No element found'}};
            }}
            """
            result = self.driver.execute_script(script)
            results['event_dispatch'] = result
            logger.info(f"事件分发结果: {result}")
        except Exception as e:
            results['event_dispatch'] = {'success': False, 'error': str(e)}
            logger.error(f"事件分发失败: {e}")
        
        return results
    
    def get_element_info(self, x, y):
        """获取指定位置的元素信息"""
        try:
            script = f"""
            var element = document.elementFromPoint({x}, {y});
            if (element) {{
                var computedStyle = window.getComputedStyle(element);
                return {{
                    tagName: element.tagName,
                    id: element.id || '',
                    className: element.className || '',
                    textContent: element.textContent ? element.textContent.trim().substring(0, 100) : '',
                    hasClick: !!element.click,
                    hasOnclick: !!element.onclick,
                    cursor: computedStyle.cursor,
                    display: computedStyle.display,
                    visibility: computedStyle.visibility,
                    pointerEvents: computedStyle.pointerEvents,
                    zIndex: computedStyle.zIndex,
                    position: computedStyle.position
                }};
            }}
            return null;
            """
            
            info = self.driver.execute_script(script)
            return info
        except Exception as e:
            logger.error(f"获取元素信息失败: {e}")
            return None
    
    def test_page_clicks(self):
        """测试页面上的点击"""
        try:
            # 访问测试页面
            logger.info("访问测试页面...")
            self.driver.get("https://app.tangoapp.dev/guide")
            time.sleep(3)
            
            # 滚动到底部
            logger.info("滚动到页面底部...")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # 测试几个常见位置的点击
            test_positions = [
                (100, 100),   # 左上角
                (500, 300),   # 中间偏左
                (800, 400),   # 中间偏右
                (1000, 600),  # 右下角区域
            ]
            
            for i, (x, y) in enumerate(test_positions, 1):
                logger.info(f"\n测试位置 {i}: ({x}, {y})")
                
                # 获取元素信息
                element_info = self.get_element_info(x, y)
                if element_info:
                    logger.info(f"元素信息: {element_info['tagName']} - {element_info['textContent'][:30]}")
                    logger.info(f"可点击性: hasClick={element_info['hasClick']}, cursor={element_info['cursor']}")
                else:
                    logger.warning("未找到元素")
                    continue
                
                # 测试点击方法
                click_results = self.test_click_methods(x, y)
                
                print(f"\n位置 ({x}, {y}) 点击测试结果:")
                for method, result in click_results.items():
                    status = "✅" if result.get('success', False) else "❌"
                    print(f"  {method}: {status} {result}")
                
                time.sleep(2)
            
        except Exception as e:
            logger.error(f"页面点击测试失败: {e}")
    
    def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("点击功能测试")
    print("="*40)
    
    tester = ClickTester()
    
    try:
        # 初始化浏览器
        if not tester.setup_driver():
            return
        
        # 测试页面点击
        tester.test_page_clicks()
        
        print("\n测试完成!")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        
    finally:
        tester.close_driver()

if __name__ == "__main__":
    main()
