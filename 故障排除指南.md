# Edge浏览器驱动故障排除指南

## 常见错误及解决方案

### 1. "Could not reach host. Are you offline?" 错误

**原因**: 网络连接问题，无法下载Edge驱动

**解决方案**:

#### 方案A: 手动下载驱动
1. 双击 `download_driver.bat` 运行驱动下载工具
2. 或者手动访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
3. 下载对应版本的EdgeDriver
4. 解压后将 `msedgedriver.exe` 放在脚本目录

#### 方案B: 检查网络连接
1. 确保网络连接正常
2. 检查防火墙设置
3. 如果使用代理，配置代理设置

#### 方案C: 升级webdriver-manager
```bash
pip install --upgrade webdriver-manager
```

### 2. "Edge浏览器驱动初始化失败" 错误

**可能原因**:
- Edge浏览器未安装
- 驱动版本不匹配
- 权限问题

**解决方案**:

#### 检查Edge浏览器
1. 确保Microsoft Edge浏览器已安装
2. 检查Edge版本: 打开Edge → 设置 → 关于Microsoft Edge

#### 手动指定驱动路径
1. 下载对应版本的EdgeDriver
2. 将 `msedgedriver.exe` 放在以下位置之一:
   - 脚本目录
   - `C:\Program Files (x86)\Microsoft\Edge\Application\`
   - 系统PATH环境变量中的任意目录

### 3. 权限问题

**解决方案**:
1. 以管理员身份运行命令提示符
2. 或者右键点击批处理文件 → "以管理员身份运行"

### 4. Python环境问题

**检查Python版本**:
```bash
python --version
```
要求: Python 3.7+

**检查依赖包**:
```bash
pip list | findstr selenium
pip list | findstr webdriver-manager
```

**重新安装依赖**:
```bash
pip install -r requirements.txt --upgrade
```

## 手动下载EdgeDriver步骤

1. **获取Edge版本**:
   - 打开Microsoft Edge
   - 点击右上角三个点 → 帮助和反馈 → 关于Microsoft Edge
   - 记录版本号 (例如: 120.0.2210.144)

2. **下载对应驱动**:
   - 访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
   - 选择对应版本的EdgeDriver
   - 下载Windows x64版本

3. **安装驱动**:
   - 解压下载的zip文件
   - 将 `msedgedriver.exe` 复制到脚本目录
   - 或者添加到系统PATH环境变量

## 验证安装

运行以下命令验证驱动是否正确安装:

```bash
# 在脚本目录运行
msedgedriver.exe --version
```

如果显示版本信息，说明安装成功。

## 联系支持

如果以上方案都无法解决问题，请提供以下信息:

1. 错误信息的完整截图
2. Edge浏览器版本
3. Python版本
4. 操作系统版本
5. 网络环境 (是否使用代理等)

## 备用方案

如果Edge驱动问题持续存在，可以考虑:

1. **使用Chrome浏览器**: 修改脚本使用Chrome驱动
2. **使用Firefox浏览器**: 修改脚本使用Firefox驱动
3. **使用本地Edge驱动**: 手动管理驱动版本
