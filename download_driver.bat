@echo off
chcp 65001 >nul
echo ========================================
echo Edge驱动手动下载工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH环境变量
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在检查requests库...
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装requests库...
    pip install requests
    if errorlevel 1 (
        echo ❌ requests库安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查完成
echo.

echo 🔽 开始下载Edge驱动...
echo ========================================
python download_edge_driver.py

echo.
echo ========================================
echo 下载完成
echo ========================================
pause
