#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cookie弹窗处理功能
用于验证Cookie弹窗是否能被正确识别和处理
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cookie_popup_detection():
    """测试Cookie弹窗检测功能"""
    
    # 设置Chrome浏览器
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    # 初始化WebDriver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        logger.info("正在访问测试网站...")
        driver.get("https://app.tangoapp.dev/guide")
        time.sleep(3)
        
        # 检查页面标题
        logger.info(f"页面标题: {driver.title}")
        
        # 常见的Cookie弹窗选择器
        cookie_selectors = [
            "button[id*='accept']",
            "button[class*='accept']",
            "button[id*='cookie']",
            "button[class*='cookie']",
            "button[id*='consent']",
            "button[class*='consent']",
            "[data-testid*='accept']",
            "[data-testid*='cookie']",
            "button:contains('Accept')",
            "button:contains('同意')",
            "button:contains('接受')",
            "button:contains('确定')",
            ".cookie-accept",
            ".accept-cookies",
            "#cookie-accept",
            "#accept-cookies"
        ]
        
        logger.info("开始检测Cookie弹窗...")
        found_popup = False
        
        for i, selector in enumerate(cookie_selectors, 1):
            try:
                script = f"""
                var elements = document.querySelectorAll('{selector}');
                var result = [];
                for (var i = 0; i < elements.length; i++) {{
                    var elem = elements[i];
                    if (elem.offsetParent !== null) {{ // 检查元素是否可见
                        result.push({{
                            tagName: elem.tagName,
                            id: elem.id || '',
                            className: elem.className || '',
                            textContent: elem.textContent ? elem.textContent.trim() : '',
                            visible: true
                        }});
                    }}
                }}
                return result;
                """
                
                elements = driver.execute_script(script)
                
                if elements:
                    found_popup = True
                    logger.info(f"✅ 选择器 {i}: {selector}")
                    for elem in elements:
                        logger.info(f"   找到元素: {elem['tagName']} - {elem['textContent'][:50]}")
                        logger.info(f"   ID: {elem['id']}, 类名: {elem['className']}")
                else:
                    logger.info(f"❌ 选择器 {i}: {selector} - 未找到元素")
                    
            except Exception as e:
                logger.error(f"❌ 选择器 {i}: {selector} - 错误: {e}")
        
        if not found_popup:
            logger.info("🔍 未检测到Cookie弹窗，或弹窗已被处理")
        
        # 尝试查找所有可能的弹窗元素
        logger.info("\n检查页面中所有可能的弹窗元素...")
        
        popup_keywords = ['cookie', 'consent', 'accept', 'agree', 'privacy', 'policy']
        
        for keyword in popup_keywords:
            try:
                script = f"""
                var elements = document.querySelectorAll('*');
                var result = [];
                for (var i = 0; i < elements.length; i++) {{
                    var elem = elements[i];
                    var text = elem.textContent ? elem.textContent.toLowerCase() : '';
                    var className = elem.className ? elem.className.toLowerCase() : '';
                    var id = elem.id ? elem.id.toLowerCase() : '';
                    
                    if ((text.includes('{keyword}') || className.includes('{keyword}') || id.includes('{keyword}')) 
                        && elem.offsetParent !== null && elem.tagName === 'BUTTON') {{
                        result.push({{
                            tagName: elem.tagName,
                            id: elem.id || '',
                            className: elem.className || '',
                            textContent: elem.textContent ? elem.textContent.trim().substring(0, 100) : ''
                        }});
                    }}
                }}
                return result.slice(0, 5); // 限制返回数量
                """
                
                elements = driver.execute_script(script)
                
                if elements:
                    logger.info(f"🔍 关键词 '{keyword}' 相关的按钮:")
                    for elem in elements:
                        logger.info(f"   {elem['tagName']} - {elem['textContent']}")
                        
            except Exception as e:
                logger.error(f"检查关键词 '{keyword}' 时出错: {e}")
        
        logger.info("\nCookie弹窗检测测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        
    finally:
        logger.info("关闭浏览器...")
        driver.quit()

def main():
    """主函数"""
    print("Cookie弹窗处理功能测试")
    print("="*40)
    
    test_cookie_popup_detection()
    
    print("\n测试建议:")
    print("1. 如果检测到Cookie弹窗，说明自动化脚本应该能够处理")
    print("2. 如果未检测到，可能弹窗已被处理或使用了不同的选择器")
    print("3. 可以根据检测结果调整脚本中的选择器列表")

if __name__ == "__main__":
    main()
